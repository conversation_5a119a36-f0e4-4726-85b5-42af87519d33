import { defineRouting } from 'next-intl/routing'

/**
 * i18n 相关配置
 */
const i18nRouting = defineRouting({
  localeDetection: false,
  localeCookie: true,
  alternateLinks: false,
  // A list of all locales that are supported
  locales: [
    'zh-<PERSON>',
    // 'en-US'
  ],
  // Used when no locale matches
  defaultLocale: 'zh-<PERSON>',
  // Configures whether and which prefix
  // localePrefix: {
  //   mode: 'always',
  //   prefixes: {
  //     'zh-<PERSON>': '/zh',
  //     'en-US': '/us',
  //   },
  // },
  localePrefix: 'never',
  // change the locale handling per domain
  domains: [
    {
      domain: 'm.ninebot.com',
      defaultLocale: 'zh-<PERSON>',
      // Optionally restrict the locales available on this domain
      // If there are no `locales` specified on a domain,
      // all available locales will be supported here
      locales: ['zh-<PERSON>'],
    },
    // {
    //   domain: "us.example.com",
    //   defaultLocale: "en",
    //   locales: ["en"],
    // },
  ],
  // The `pathnames` object holds pairs of internal and
  // external paths. Based on the locale, the external
  // paths are rewritten to the shared, internal ones.
  pathnames: {},
  // The `matcher` is relative to the `basePath`
  // matcher: [
  // This entry handles the root of the base
  // path and should always be included
  // '/',
  // ... other matcher config
  // ],
})

export default i18nRouting
