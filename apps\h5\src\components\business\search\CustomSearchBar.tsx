'use client'

import React, { useEffect, useRef, useState } from 'react'
import { useTranslations } from 'next-intl'
import {
  appLocalStorage,
  ROUTE,
  TRACK_EVENT,
  useDebounceFn,
  useSearchRelationWordsQuery,
  useVolcAnalytics,
} from '@ninebot/core'
import { useNavigate } from '@ninebot/core/src/businessHooks'
import { GetSearchRelationWordsQuery } from '@ninebot/core/src/graphql/generated/graphql'
import { Input, InputRef } from 'antd-mobile'

import { useRouter } from '@/i18n/navigation'

type CustomSearchBarProps = {
  searchValue: string
  setSearchValue: React.Dispatch<React.SetStateAction<string>>
  onSearch?: (value: string) => void
  onCancel: () => void
  placeholder?: string
  setCanShow?: React.Dispatch<React.SetStateAction<boolean>>
  isResultPage?: boolean
}

type SearchSuggestions = NonNullable<
  NonNullable<GetSearchRelationWordsQuery['search_relation_words']>
>['items']

const SearchIcon = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M13.3327 8.33342C13.3327 11.0948 11.0941 13.3334 8.33268 13.3334C5.57126 13.3334 3.33268 11.0948 3.33268 8.33342C3.33268 5.57199 5.57126 3.33341 8.33268 3.33341C11.0941 3.33341 13.3327 5.57199 13.3327 8.33342ZM12.4207 13.6C11.2918 14.4775 9.87328 15.0001 8.33268 15.0001C4.65078 15.0001 1.66602 12.0153 1.66602 8.33342C1.66602 4.65152 4.65078 1.66675 8.33268 1.66675C12.0146 1.66675 14.9993 4.65152 14.9993 8.33342C14.9993 9.87401 14.4768 11.2925 13.5992 12.4215L18.5053 17.3276L17.3268 18.5061L12.4207 13.6Z"
      fill="#86868B"
    />
  </svg>
)

const CloseIcon = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M0 10C0 4.47715 4.47715 0 10 0C15.5228 0 20 4.47715 20 10C20 15.5228 15.5228 20 10 20C4.47715 20 0 15.5228 0 10Z"
      fill="#D1D1D4"
    />
    <path
      d="M6.46536 5.67941L5.67969 6.46509L8.82238 9.60778L9.60806 9.60778L9.60806 8.82211L6.46536 5.67941Z"
      fill="black"
    />
    <path
      d="M5.68015 13.5361L6.46582 14.3218L9.60852 11.1791L9.60852 10.3934L8.82284 10.3934L5.68015 13.5361Z"
      fill="black"
    />
    <path
      d="M14.3218 13.5361L13.5361 14.3218L10.3934 11.1791L10.3934 10.3934L11.1791 10.3934L14.3218 13.5361Z"
      fill="black"
    />
    <path
      d="M14.3218 6.46512L13.5361 5.67944L10.3934 8.82214L10.3934 9.60781L11.1791 9.60781L14.3218 6.46512Z"
      fill="black"
    />
  </svg>
)

export default function CustomSearchBar({
  searchValue,
  setSearchValue,
  onSearch,
  onCancel,
  placeholder = '',
  setCanShow,
  isResultPage = false,
}: CustomSearchBarProps) {
  const router = useRouter()
  const { reportEvent } = useVolcAnalytics()
  const getI18nString = useTranslations('Common')
  const [suggestions, setSuggestions] = useState<SearchSuggestions>([])
  const { openPage } = useNavigate()
  const [shouldSkip, setShouldSkip] = useState(isResultPage)
  const [defaultValue, setDefaultValue] = useState(searchValue || placeholder)
  const inputRef = useRef<InputRef>(null)

  const { data: relationWords } = useSearchRelationWordsQuery(
    {
      searchWords: searchValue?.trim(),
      currentPage: 1,
      pageSize: 9999,
    },
    { skip: shouldSkip || !searchValue?.trim() },
  )

  // 搜索建议处理函数
  const handleSearch = (value: string) => {
    setShouldSkip(false)
    if (!value?.trim()) {
      setCanShow?.(true)
      setSuggestions([])
      setSearchValue('')
    } else {
      setSearchValue(value)
    }
  }

  // 搜索提交处理函数
  const { run: handleSearchSubmit } = useDebounceFn((value: string) => {
    if (value.trim()) {
      handleSearchHistory(value)
      setSuggestions([])
      onSearch?.(value.trim())
      // 添加路由跳转到搜索结果页
      if (!isResultPage) {
        openPage({
          route: ROUTE.searchResult,
          value: value.trim(),
        })
      } else {
        setShouldSkip(true)
        setSearchValue(value.trim())
        setCanShow?.(true)
        router.replace(`/search/result?q=${encodeURIComponent(value.trim())}`)
      }
    }
  })

  const handleSearchHistory = async (word: string) => {
    const searchHistory: string = (await appLocalStorage.getItem('search_history')) || '[]'

    const newSearchHistory = JSON.parse(searchHistory)

    const newSearchHistoryList = [
      word.trim(),
      ...newSearchHistory.filter((item: string) => item !== word.trim()),
    ]

    await appLocalStorage.setItem('search_history', JSON.stringify(newSearchHistoryList))
  }

  // 取消搜索
  const handleCancel = () => {
    if (isResultPage) {
      reportEvent(TRACK_EVENT.shop_searchresult_cancel_button_click, {
        button_id: 'shop_searchresult_cancel',
      })
    } else {
      reportEvent(TRACK_EVENT.shop_searchpage_cancel_button_click, {
        button_id: 'shop_searchpage_cancel',
      })
    }

    setSearchValue('')
    setSuggestions([])
    onCancel?.()
  }

  useEffect(() => {
    if (
      relationWords?.search_relation_words?.items &&
      relationWords?.search_relation_words?.items?.length > 0
    ) {
      setSuggestions(relationWords.search_relation_words.items)
      setCanShow?.(false)
    } else {
      setSuggestions([])
      setCanShow?.(true)
    }
  }, [relationWords, setCanShow])

  useEffect(() => {
    setDefaultValue(searchValue)
  }, [searchValue])

  useEffect(() => {
    // 组件挂载后自动聚焦
    if (inputRef.current) {
      setTimeout(() => {
        inputRef.current?.focus()
      }, 200)
    }
  }, [])

  return (
    <>
      <div className="sticky top-0 z-50 flex h-[56px] items-center justify-between gap-base-16 bg-white py-4">
        <div className="relative flex-1 rounded-full bg-gray-base">
          <div
            className="absolute left-base-12 top-1/2 -translate-y-1/2"
            onClick={() => handleSearchSubmit(searchValue || placeholder)}>
            <SearchIcon />
          </div>
          <Input
            className="search-input h-[36px] pl-[44px]"
            clearable
            ref={inputRef}
            autoFocus
            onlyShowClearWhenFocus={false}
            clearIcon={defaultValue || searchValue ? <CloseIcon /> : null}
            placeholder={placeholder}
            value={searchValue || defaultValue}
            onEnterPress={(e) =>
              handleSearchSubmit((e.target as HTMLInputElement).value || placeholder)
            }
            onChange={(value) => handleSearch(value)}
            onClear={() => {
              setSearchValue('')
              setSuggestions([])
              setCanShow?.(true)
            }}
            style={{
              '--font-size': '14px',
              '--color': '#0F0F0F',
            }}
            onClick={() => {
              if (isResultPage) {
                reportEvent(TRACK_EVENT.shop_searchresult_search_click, {
                  search_word: searchValue?.trim(),
                })
              } else {
                reportEvent(TRACK_EVENT.shop_searchpage_search_click, {
                  search_word: searchValue?.trim(),
                })
              }
            }}
          />
        </div>
        <button className="text-label-base" onClick={handleCancel}>
          {getI18nString('cancel')}
        </button>
      </div>

      {/* 搜索建议列表 */}
      {searchValue && suggestions && suggestions?.length > 0 && (
        <div className="bg-white">
          {suggestions.map((item, index) => (
            <button
              key={index}
              className="flex h-[41px] w-full items-center text-left font-miSansRegular330 text-[14px] leading-[17px] text-[#000000]"
              onClick={() => handleSearchSubmit(item?.words || '')}>
              <div
                className="truncate"
                dangerouslySetInnerHTML={{
                  __html: item?.words
                    ? item.words.replace(
                        new RegExp(searchValue, 'gi'),
                        `<span class="text-primary">${searchValue}</span>`,
                      )
                    : '',
                }}
              />
            </button>
          ))}
        </div>
      )}
    </>
  )
}
