'use client'
import { useCallback, useEffect, useReducer, useRef } from 'react'
import Image from 'next/image'
import { useTranslations } from 'next-intl'
import {
  IconPlus,
  NCoinView,
  Price,
  resolveCatchMessage,
  TCatchMessage,
  useDebounceFn,
  useNavigate,
  useOrderDetail,
  useToastContext,
} from '@ninebot/core'
import loadingIcon from '@ninebot/core/src/assets/images/loading-dark.webp'
import { ROUTE } from '@ninebot/core/src/constants'
import {
  useGetOrderDetailQuery,
  useLazyCheckOrderPayQuery,
  useLazyGetOrderH5PayQuery,
} from '@ninebot/core/src/services'
import { formatTimestamp, isIOS, sleep } from '@ninebot/core/src/utils'

import {
  AVAILABLE_PAYMENTS,
  PAYMENT_CONFIG,
  PAYMENT_STATUS,
  PaymentAction,
  PaymentState,
  SearchParams,
} from './types'
/**
 * 支付状态reducer
 * 用于管理支付流程中的各种状态
 */
const paymentReducer = (state: PaymentState, action: PaymentAction): PaymentState => {
  switch (action.type) {
    case 'SET_OPENED':
      return { ...state, isOpened: action.payload }
    case 'SET_SHOULD_POLL':
      return { ...state, shouldPoll: action.payload }
    case 'SET_INITIALIZED':
      return { ...state, isInitialized: action.payload }
    case 'SET_PAGE_VISIBLE':
      return { ...state, isPageVisible: action.payload }
    case 'RESET_STATE':
      return {
        isOpened: false,
        shouldPoll: false,
        isInitialized: false,
        isPageVisible: true,
      }
    default:
      return state
  }
}

/**
 * 支付中页面
 * 用于显示支付进行中的状态，并处理支付结果
 *
 * 支付流程：
 * 1. 用户进入支付页面，初始化支付订单
 * 2. 根据支付方式获取支付链接并跳转到支付应用
 * 3. 跳转时页面变为不可见，此时标记支付已打开(isOpened=true)
 * 4. 用户完成支付后返回浏览器，页面变为可见
 * 5. 检测到页面重新可见且支付已打开，才开始轮询订单状态
 * 6. 根据轮询结果，导航到支付结果页面
 */
export default function Page({ searchParams }: { searchParams: SearchParams }) {
  const { orderId, paymentMethodCode } = searchParams

  const getI18nString = useTranslations('Common')
  const toast = useToastContext()
  const { openPage } = useNavigate()

  // 获取订单详情
  const { data: response } = useGetOrderDetailQuery(
    {
      filter: {
        nid: { eq: orderId },
      },
    },
    {
      refetchOnMountOrArgChange: true,
    },
  )

  const orderData = response?.customer?.orders?.items?.[0]
  const { hasNCoin, totalAmount, nCoin } = useOrderDetail(orderData)

  // 状态管理
  const [state, dispatch] = useReducer(paymentReducer, {
    isOpened: false, // 是否已打开支付页面
    shouldPoll: false, // 是否应该轮询支付状态
    isInitialized: false, // 是否已初始化
    isPageVisible: true, // 页面是否可见
  })

  // 记录轮询次数和支付处理状态
  const pollingCountRef = useRef(0)
  const processingPaymentRef = useRef(false) // 防止重复处理支付结果
  const timeoutIdRef = useRef<NodeJS.Timeout | null>(null) // 存储timeout引用
  const isPollingActiveRef = useRef(false) // 标记轮询是否处于活动状态
  const maxPaymentTimeoutRef = useRef<NodeJS.Timeout | null>(null) // 存储最大支付超时定时器引用

  // API 查询hooks
  const [getOrderH5PayQuery] = useLazyGetOrderH5PayQuery()
  const [checkOrderPayQuery, { currentData: checkOrderPayData }] = useLazyCheckOrderPayQuery()

  /**
   * 打开链接
   * @param url 链接
   * @returns 是否成功打开链接
   */ // 存储当前支付 iframe 引用
  const iframeRef = useRef<HTMLIFrameElement | null>(null)

  // 清理 iframe
  const cleanupIframe = useCallback(() => {
    if (iframeRef.current && iframeRef.current.parentNode) {
      iframeRef.current.parentNode.removeChild(iframeRef.current)
      iframeRef.current = null
    }
  }, [])

  /**
   * 处理导航到结果页面
   * @param isSuccess 支付是否成功
   */ const handleNavigate = useCallback(
    (isSuccess: boolean) => {
      // 停止轮询
      if (timeoutIdRef.current) {
        clearTimeout(timeoutIdRef.current)
        timeoutIdRef.current = null
      }
      // 清除最大支付时间定时器
      if (maxPaymentTimeoutRef.current) {
        clearTimeout(maxPaymentTimeoutRef.current)
        maxPaymentTimeoutRef.current = null
      }
      // 清理 iframe
      cleanupIframe()
      isPollingActiveRef.current = false
      dispatch({ type: 'SET_SHOULD_POLL', payload: false })

      // 导航到结果页
      openPage({
        route: ROUTE.checkoutResult,
        queryParams: {
          orderId,
          isSuccess,
          paymentMethodCode,
        },
        replace: true,
      })
    },
    [orderId, openPage, cleanupIframe, paymentMethodCode],
  )

  const openLink = useCallback(
    async (url: string): Promise<boolean> => {
      try {
        // 兼容处理 Safari 浏览器
        if (isIOS()) {
          window.location.href = url
          return true
        }

        // 清理之前的 iframe
        cleanupIframe()

        // 创建新的 iframe
        const iframe = document.createElement('iframe')
        iframe.style.display = 'none'
        iframe.src = url
        document.body.appendChild(iframe)
        iframeRef.current = iframe

        // 设置最大支付时间为1分钟
        if (maxPaymentTimeoutRef.current) {
          clearTimeout(maxPaymentTimeoutRef.current)
        }
        maxPaymentTimeoutRef.current = setTimeout(
          () => {
            // 如果1分钟内没有完成支付，则跳转到失败页面
            if (!isPollingActiveRef.current) {
              cleanupIframe() // 清理 iframe
              toast.show({
                icon: 'fail',
                content: getI18nString('payment_timeout'),
              })
              handleNavigate(false)
            }
          },
          1 * 60 * 1000,
        )

        // 延迟一下返回true，确保跳转发生
        await sleep(100)
        return true
      } catch (error) {
        console.error('链接打开失败:', error)
        cleanupIframe() // 清理 iframe
        await sleep(500)
        toast.show({
          icon: 'fail',
          content: getI18nString('open_link_error'),
        })
        return false
      }
    },
    [toast, getI18nString, handleNavigate, cleanupIframe],
  )

  /**
   * 处理错误情况
   * @param error 错误信息
   * @param shouldNavigate 是否需要导航到结果页
   */
  const handleError = useCallback(
    (error: TCatchMessage, shouldNavigate = true) => {
      toast.show({
        icon: 'fail',
        content: resolveCatchMessage(error) as string,
      })

      if (shouldNavigate) {
        handleNavigate(false)
      }
    },
    [toast, handleNavigate],
  )

  /**
   * 获取订单支付信息
   * 获取第三方支付所需的URL链接
   */
  const fetchOrderH5Pay = useCallback(
    async (id: string) => {
      try {
        const response = await getOrderH5PayQuery({
          orderId: id,
          payCode: paymentMethodCode,
        }).unwrap()

        if (response?.get_order_h5_pay?.code !== PAYMENT_STATUS.SUCCESS) {
          throw new Error(response?.get_order_h5_pay?.message || getI18nString('fetch_data_error'))
        }

        return response?.get_order_h5_pay?.sdkData
      } catch (error) {
        handleError(error as TCatchMessage)
        return null
      }
    },
    [getOrderH5PayQuery, paymentMethodCode, handleError, getI18nString],
  )

  /**
   * 停止轮询支付状态
   */
  const stopPolling = useCallback(() => {
    if (timeoutIdRef.current) {
      clearTimeout(timeoutIdRef.current)
      timeoutIdRef.current = null
    }
    isPollingActiveRef.current = false
    dispatch({ type: 'SET_SHOULD_POLL', payload: false })
  }, [])

  /**
   * 执行单次支付状态查询
   * 处理各种支付状态结果
   */
  const runCheckPaymentStatus = useCallback(async () => {
    // 如果轮询已停止，不继续执行
    if (!isPollingActiveRef.current) {
      return
    }

    if (processingPaymentRef.current) {
      // 设置下一次轮询
      timeoutIdRef.current = setTimeout(() => {
        void runCheckPaymentStatus()
      }, PAYMENT_CONFIG.POLLING_INTERVAL)
      return
    }

    try {
      processingPaymentRef.current = true
      const response = await checkOrderPayQuery({
        orderId: orderId || '',
        action: 'query',
      }).unwrap()

      if (response?.check_order_pay) {
        pollingCountRef.current += 1
        const { code, message } = response.check_order_pay

        // 支付成功
        if (code === PAYMENT_STATUS.SUCCESS) {
          stopPolling()
          handleNavigate(true)
          return
        }

        // 支付取消
        if (code === PAYMENT_STATUS.CANCELLED) {
          stopPolling()
          toast.show({
            icon: 'fail',
            content: message || getI18nString('order_cancelled'),
          })
          handleNavigate(false)
          return
        }

        // 轮询超最大次数，但订单仍可支付
        if (
          pollingCountRef.current >= PAYMENT_CONFIG.MAX_POLLING_COUNT &&
          code === PAYMENT_STATUS.CAN_PAY
        ) {
          stopPolling()
          handleNavigate(false)
          return
        }
      }

      // 如果轮询仍然活跃，设置下一次轮询
      if (isPollingActiveRef.current) {
        timeoutIdRef.current = setTimeout(() => {
          void runCheckPaymentStatus()
        }, PAYMENT_CONFIG.POLLING_INTERVAL)
      }
    } catch (error) {
      console.error('轮询查询失败:', error)
      toast.show({
        icon: 'fail',
        content: getI18nString('payment_check_failed'),
      })
      stopPolling()
      handleNavigate(false)
    } finally {
      processingPaymentRef.current = false
    }
  }, [
    checkOrderPayQuery,
    orderId,
    handleNavigate,
    toast,
    getI18nString,
    stopPolling,
    isPollingActiveRef,
  ])

  // 使用防抖函数包装查询，防止过于频繁的调用
  const { run: debouncedCheckStatus } = useDebounceFn(runCheckPaymentStatus, {
    wait: 500,
    leading: true,
    trailing: false,
  })

  /**
   * 开始轮询支付状态
   * 确保轮询不会重复启动
   */
  const startPolling = useCallback(() => {
    // 先停止之前的轮询
    if (timeoutIdRef.current) {
      clearTimeout(timeoutIdRef.current)
      timeoutIdRef.current = null
    }

    // 清除最大支付时间定时器，因为已经开始轮询了
    if (maxPaymentTimeoutRef.current) {
      clearTimeout(maxPaymentTimeoutRef.current)
      maxPaymentTimeoutRef.current = null
    }

    // 重置处理状态
    processingPaymentRef.current = false
    isPollingActiveRef.current = true
    dispatch({ type: 'SET_SHOULD_POLL', payload: true })

    // 立即执行一次查询
    debouncedCheckStatus()
  }, [debouncedCheckStatus])

  /**
   * 监听页面可见性变化
   */
  useEffect(() => {
    const handleVisibilityChange = () => {
      const isVisible = document.visibilityState === 'visible'
      dispatch({ type: 'SET_PAGE_VISIBLE', payload: isVisible })

      // 如果页面不可见，且之前未打开支付，则认为用户正在进行支付
      if (!state.isOpened && !isVisible) {
        dispatch({ type: 'SET_OPENED', payload: true })
      }

      // 当页面重新变为可见且已经打开过支付，立即开始轮询
      if (isVisible && state.isOpened) {
        // 无论之前是否有轮询，都重新启动
        startPolling()
      }
    }

    document.addEventListener('visibilitychange', handleVisibilityChange)
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange)
      // 组件卸载时清理所有定时器和 iframe
      if (timeoutIdRef.current) {
        clearTimeout(timeoutIdRef.current)
        timeoutIdRef.current = null
      }
      if (maxPaymentTimeoutRef.current) {
        clearTimeout(maxPaymentTimeoutRef.current)
        maxPaymentTimeoutRef.current = null
      }
      cleanupIframe()
      isPollingActiveRef.current = false
    }
  }, [state.isOpened, startPolling, cleanupIframe])

  /**
   * 组件挂载时检查参数并初始化
   */
  useEffect(() => {
    // 参数校验
    if (!orderId || !paymentMethodCode) {
      toast.show({
        icon: 'fail',
        content: getI18nString('invalid_payment_params'),
      })
      handleNavigate(false)
      return
    }

    // 初始化支付
    if (!state.isInitialized && orderId && paymentMethodCode) {
      const initPayment = async () => {
        dispatch({ type: 'SET_INITIALIZED', payload: true })

        // 创建支付订单
        const response = await checkOrderPayQuery({
          orderId,
          action: 'create',
        }).unwrap()

        if (response?.check_order_pay?.code === PAYMENT_STATUS.CANCELLED) {
          toast.show({
            icon: 'fail',
            content: response.check_order_pay.message || getI18nString('order_cancelled'),
          })

          handleNavigate(false)
        }
      }

      initPayment()
    }
  }, [
    checkOrderPayQuery,
    state.isInitialized,
    orderId,
    paymentMethodCode,
    handleNavigate,
    toast,
    getI18nString,
  ])

  /**
   * 处理支付创建结果
   * 根据支付结果执行不同操作
   */
  useEffect(() => {
    // 避免重复处理或在未收到数据时处理
    if (state.isOpened || processingPaymentRef.current || !checkOrderPayData?.check_order_pay) {
      return
    }

    const { code } = checkOrderPayData.check_order_pay

    const handlePaymentResult = async () => {
      processingPaymentRef.current = true
      try {
        switch (code) {
          // 可以支付：根据支付方式打开相应支付页面
          case PAYMENT_STATUS.CAN_PAY:
            if (paymentMethodCode === AVAILABLE_PAYMENTS.huifu_alipay) {
              const response = await fetchOrderH5Pay(orderId)
              if (!response?.alipayUrl) {
                throw new Error(getI18nString('fetch_data_error'))
              }

              const status = await openLink(response.alipayUrl || '')

              if (!status) {
                handleNavigate(false)
              }
            } else if (paymentMethodCode === AVAILABLE_PAYMENTS.huifu_wechat) {
              const response = await fetchOrderH5Pay(orderId)
              if (!response?.wechatDirectUrl) {
                throw new Error(getI18nString('fetch_data_error'))
              }

              const status = await openLink(response.wechatDirectUrl || '')
              if (!status) {
                handleNavigate(false)
              }
            } else {
              throw new Error(getI18nString('not_support_payment_method'))
            }
            break

          // 支付成功：直接导航到成功页面
          case PAYMENT_STATUS.SUCCESS:
            handleNavigate(true)
            break

          // 支付失败：显示错误并询问是否重试
          case PAYMENT_STATUS.FAILED:
            await sleep(500)
            handleNavigate(false)
            break
        }
      } catch (error) {
        handleError(error as TCatchMessage)
        processingPaymentRef.current = false
      }
    }

    handlePaymentResult()
  }, [
    checkOrderPayData,
    fetchOrderH5Pay,
    openLink,
    state.isOpened,
    orderId,
    paymentMethodCode,
    handleNavigate,
    handleError,
    getI18nString,
    checkOrderPayQuery,
  ])

  return (
    <div className="flex h-[508px] w-full flex-col items-center justify-center px-[18px]">
      <div className="mb-base-16 flex h-[68px] w-[68px] items-center justify-center">
        <Image priority src={loadingIcon} loading="eager" width={30} height={2} alt="Loading..." />
      </div>

      <div className="text-center font-miSansDemiBold450 text-[22px] leading-[1.2]">支付中</div>

      <div className="mb-[24px] mt-[32px] w-full border-t border-[#00000010]"></div>

      {orderData && (
        <div className="flex w-full flex-col gap-base-12 font-miSansRegular330 text-[14px] leading-[1.4] text-[#86868B]">
          <div className="flex items-center justify-between">
            <span>订单编号：</span>
            <span className="text-[#0F0F0F]">{orderData.number}</span>
          </div>
          <div className="flex items-center justify-between">
            <span>下单时间：</span>
            <span className="text-[#0F0F0F]">{formatTimestamp(orderData.order_date)}</span>
          </div>
          <div className="flex items-center justify-between">
            <span>订单金额：</span>
            {hasNCoin ? (
              <div className="flex items-center">
                <Price
                  price={totalAmount}
                  currencyStyle="text-[14px] leading-[1.4] text-[#0F0F0F]"
                  textStyle="text-[14px] leading-[1.4] text-[#0F0F0F]"
                  fractionStyle="text-[14px] leading-[1.4] text-[#0F0F0F]"
                />
                <div className="mx-[4px]">
                  <IconPlus />
                </div>
                <NCoinView
                  number={nCoin}
                  iconStyle={{ size: 16 }}
                  textStyle="text-[14px] leading-[1.4] text-[#0F0F0F]"
                />
              </div>
            ) : (
              <Price
                price={totalAmount}
                currencyStyle="text-[14px] leading-[1.4] text-[#0F0F0F]"
                textStyle="text-[14px] leading-[1.4] text-[#0F0F0F]"
                fractionStyle="text-[14px] leading-[1.4] text-[#0F0F0F]"
              />
            )}
          </div>
        </div>
      )}
    </div>
  )
}
